# ORC MongoDB服务 - 开发环境配置文件
# 版本: 3.0.0 (重构版本)
# 基于生产环境配置，调整为开发环境参数

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "3.0.0"
  environment: "development"

# ==================== 环境配置 ====================
environment:
  project_root: "/workdir/RenL/User-DF/"

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: "logs/orc_mongodb_service/orc_mongodb_service.log"
  file_max_size: "100MB"
  file_backup_count: 10
  console_enabled: true
  console_colored: true
  structured: false

# ==================== ORC处理配置 ====================
orc_processor:
  # 日期和省份配置 - 开发环境使用较小范围
  start_date: "20250717"
  end_date: "20250717"
  province_ids: [100, 200]  # 开发环境只处理部分省份

  # ORC文件配置
  orc_base_path: "test_data/hive_data/tw_user_pic_daily_aggregation"
  orc_file_pattern: "*"

  # 列名映射
  column_mapping:
    uid_columns: ["id", "uid", "user_id", "userid", "UID", "USER_ID"]
    pid_columns: ["pic_id_list", "pid_list", "pid", "product_id", "item_id", "PID", "PRODUCT_ID"]

  # 数据处理配置 - 开发环境减少数量
  max_pids_per_user: 50
  enable_pid_deduplication: true
  sort_pids_by_timestamp: true
  group_pids_by_timestamp: true

  # 分批处理配置 - 开发环境使用较小批次
  batch_processing:
    batch_size: 100
    pid_query_batch_size: 1000
    enable_batch_optimization: true

  # 性能优化配置 - 开发环境降低资源使用
  performance:
    memory:
      gc_threshold: 0.7
      cleanup_interval: 60
    io:
      read_buffer_size: 8192
      write_buffer_size: 8192
      use_async_io: true
    concurrency:
      max_concurrent_files: 2
      max_concurrent_batches: 5

# ==================== MongoDB写入服务配置 ====================
mongodb_writer_service:
  host: "0.0.0.0"
  port: 8002

  # 批处理配置
  batch_processing:
    batch_size: 5

  # 重试配置
  retry:
    max_retries: 2
    delay: 2

  # 清理配置
  cleanup:
    delay: 60

# ==================== 监控服务配置 ====================
monitoring_service:
  # 监控配置
  monitoring:
    check_interval: 10

  # 服务URL配置
  services:
    orc_processor:
      url: "http://localhost:8001"
    mongodb_writer:
      url: "http://localhost:8002"

# ==================== Milvus配置 ====================
milvus:
  connection:
    uri: "http://localhost:19530"  # 开发环境使用本地Milvus
    token: ""
    database: "default"

    pool:
      max_connections: 10
      min_connections: 2
      timeout: 15
      max_retries: 3
      retry_delay: 1.0

  collections:
    content_collection: "content_tower_collection_20250616"
    user_collection: "user_tower_collection"

  vector_dimensions:
    content_vector_dim: 512
    user_vector_dim: 256

  batch_processing:
    timeout: 300
    concurrent_batches: 3

# 是否启用Milvus PID过滤 - 开发环境可以关闭以加快测试
enable_milvus_filtering: false

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 6379  # 开发环境使用标准Redis端口
  db: 0
  password: ""

  # 队列配置 - 开发环境使用较小队列（保留兼容性）
  queue_name: "mongodb_write_queue"
  max_queue_size: 50

  # Redis Stream配置 - 开发环境
  stream:
    # Stream名称
    stream_name: "mongodb_write_stream_dev"
    # Stream最大长度（开发环境使用较小值）
    max_length: 1000
    # 是否使用近似最大长度
    approximate_max_length: true
    # 消息TTL（秒）
    message_ttl: 1800

    # 消费者组配置
    consumer_group:
      group_name: "mongodb_writers_dev"
      consumer_name: "writer_dev_001"
      start_id: "0"  # 从头开始消费

      # 消费配置
      block_time: 1000  # 阻塞等待时间（毫秒）
      count: 5  # 每次读取的消息数量（开发环境较小）

      # 重试配置
      pending_timeout: 180  # 待处理消息超时时间（秒）
      max_delivery_count: 2  # 最大投递次数

  # 队列长度控制配置 - 开发环境使用较小阈值
  queue_control:
    check_interval: 5
    pause_threshold: 30
    resume_threshold: 10

  # 连接池配置
  connection_pool:
    max_connections: 10
    retry_on_timeout: true

# ==================== MongoDB配置 ====================
mongodb:
  # 连接配置
  connection:
    host: "localhost"
    port: 27017
    database: "nrdc_dev"  # 开发环境使用不同数据库
    username: ""
    password: ""

    # 连接池配置 - 开发环境减少连接数
    pool:
      max_pool_size: 20
      min_pool_size: 2
      max_idle_time_ms: 30000
      connect_timeout_ms: 5000
      server_selection_timeout_ms: 15000

  # 集合配置
  collection: "user_pid_records_optimized"

  # 缓存配置 - 开发环境使用较小缓存
  cache:
    size_mb: 256

  # 写入优化配置
  write_concern:
    w: 1
    j: false
    wtimeout: 5000

  # 批量操作配置
  bulk_operations:
    batch_size: 100
    ordered: false
    bypass_document_validation: false

# ==================== 延迟控制配置 ====================
delay_control:
  # 批次间延迟（毫秒） - 开发环境可以设置更短延迟
  batch_delay: 50
  # 文件处理完成后延迟计算
  file_delay:
    delay_multiplier: 0.1

